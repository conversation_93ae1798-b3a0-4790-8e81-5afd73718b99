import asyncio
import json
import time
from datetime import datetime
from aiohttp import web, web_request
from aiohttp.web_response import Response
from config.logger import setup_logging

from core.utils.redis_client import RedisClient
from core.utils.llm import create_instance
from core.auth import AuthMiddleware, AuthenticationError

TAG = __name__


class HttpApiServer:
    def __init__(self, config: dict, websocket_server=None):
        self.config = config
        self.websocket_server = websocket_server
        self.logger = setup_logging()
        self.start_time = time.time()

        # 初始化认证中间件
        self.auth = AuthMiddleware(config)

        # 初始化Redis客户端
        self.redis_client = RedisClient(config) if config.get("server", {}).get("redis", {}).get("enabled", False) else None

        # 初始化LLM
        self._init_llm()

    async def _authenticate_request(self, request: web_request.Request) -> bool:
        """验证HTTP请求的认证信息"""
        try:
            # 从HTTP headers中提取认证信息
            headers = {
                'client-id': request.headers.get('client-id', ''),
                'device-id': request.headers.get('device-id', ''),
                'authorization': request.headers.get('authorization', '')
            }
            
            # 使用AuthMiddleware进行认证
            await self.auth.authenticate(headers)
            
            # 认证成功，记录请求信息
            client_id = headers.get('client-id', 'unknown')
            device_id = headers.get('device-id', 'unknown')
            self.logger.bind(tag=TAG).info(f"HTTP API认证成功 - Path: {request.path}, Client: {client_id}, Device: {device_id}")
            return True
            
        except AuthenticationError as e:
            self.logger.bind(tag=TAG).warning(f"HTTP API认证失败 - Path: {request.path}, Error: {e}")
            return False
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"HTTP API认证过程中发生错误 - Path: {request.path}, Error: {e}")
            return False

    def _create_auth_error_response(self, message: str = "认证失败") -> Response:
        """创建认证失败的响应"""
        response = web.json_response({
            "success": False,
            "message": message,
            "error_code": "AUTHENTICATION_FAILED",
            "timestamp": datetime.now().isoformat()
        }, status=401)
        return self._add_cors_headers(response)

    def _init_llm(self):
        """初始化LLM实例"""
        try:
            selected_module = self.config.get("selected_module", {})
            llm_name = selected_module.get("LLM")
            if llm_name:
                llm_config = self.config.get("LLM", {}).get(llm_name, {})
                if llm_config:
                    llm_type = llm_config.get("type")
                    if llm_type:
                        self.llm = create_instance(llm_type, llm_config)
                        self.logger.bind(tag=TAG).info(f"LLM初始化成功: {llm_name} ({llm_type})")
                    else:
                        self.logger.bind(tag=TAG).warning(f"LLM配置中缺少type字段: {llm_name}")
                        self.llm = None
                else:
                    self.logger.bind(tag=TAG).warning(f"找不到LLM配置: {llm_name}")
                    self.llm = None
            else:
                self.logger.bind(tag=TAG).warning("未配置LLM模块")
                self.llm = None
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"LLM初始化失败: {e}")
            self.llm = None

    async def start(self):
        """启动HTTP API服务器"""
        server_config = self.config["server"]
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("http_api_port", 8100))

        app = web.Application()

        # 添加路由
        self._setup_routes(app)

        # 运行服务
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()

        self.logger.bind(tag=TAG).info(f"HTTP API服务器启动成功，监听端口: {port}")

        # 【重要修复】添加取消信号避免无限轮询导致CPU占用
        self._stop_event = asyncio.Event()

        # 保持服务运行
        try:
            while True:
                try:
                    # 【修复】使用带超时的等待，支持优雅关闭
                    await asyncio.wait_for(self._stop_event.wait(), timeout=3600)
                    self.logger.bind(tag=TAG).info("HTTP API服务器收到停止信号")
                    break
                except asyncio.TimeoutError:
                    # 超时是正常的，继续等待
                    continue
        except asyncio.CancelledError:
            self.logger.bind(tag=TAG).info("HTTP API服务器被取消")
            raise
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"HTTP API服务器异常: {e}")
            raise

    def _add_cors_headers(self, response):
        """添加CORS头"""
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, client-id, device-id'
        return response

    def _setup_routes(self, app: web.Application):
        """设置API路由"""
        # 主要功能路由
        app.router.add_post('/api/v1/set_user_profile', self._handle_set_user_profile)
        app.router.add_post('/api/v1/text_summary', self._handle_text_summary)

        # OPTIONS请求处理（用于CORS预检）
        app.router.add_options('/{path:.*}', self._handle_options)

        # 根路径
        app.router.add_get('/', self._handle_root)

    async def _handle_options(self, request: web_request.Request) -> Response:
        """处理OPTIONS预检请求"""
        response = web.Response(status=200)
        return self._add_cors_headers(response)

    async def _handle_root(self, request: web_request.Request) -> Response:
        """处理根路径请求"""
        response = web.json_response({
            "message": "YuYan HTTP API Server",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "authentication": {
                "required_headers": ["client-id", "authorization"],
                "authorization_format": "Bearer <token>",
                "note": "所有API端点都需要认证"
            },
            "endpoints": [
                "POST /api/v1/set_user_profile - 设置用户档案并生成个性化问候语",
                "POST /api/v1/text_summary - 文本摘要生成会议纪要"
            ]
        })
        return self._add_cors_headers(response)

    async def _handle_set_user_profile(self, request: web_request.Request) -> Response:
        """设置用户档案并生成个性化问候语"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
                
            # 解析请求数据
            data = await request.json()

            # 记录客户端请求数据
            self.logger.bind(tag=TAG).info(f"[用户档案接口] 收到客户端请求: {json.dumps(data, ensure_ascii=False)}")

            # 验证必要字段
            if not data or not isinstance(data, dict):
                response = web.json_response({
                    "success": False,
                    "message": "请求数据格式错误，需要JSON格式"
                }, status=400)
                return self._add_cors_headers(response)

            # 获取用户信息
            user_name = data.get("user_name", "")
            lifestyle = data.get("lifestyle", "")
            interests = data.get("interests", "")
            client_id = data.get("client_id", "")

            # 获取AI相关信息
            ai_gender = data.get("ai_gender", "")  # 用户希望的AI性别：男/女
            ai_name = data.get("ai_name", "")      # 用户希望给AI起的名字

            # 容错处理：user_name可以为空，不再强制要求

            # 保存用户数据到Redis并设置AI角色
            if self.redis_client and self.redis_client.enabled and client_id:
                try:
                    # 根据AI性别确定角色
                    ai_role = self._determine_ai_role(ai_gender)

                    # 获取现有用户数据
                    user_data = self.redis_client.get_user_data(client_id) or {}

                    # 容错处理：只有当user_name不为空时才保存该字段
                    if user_name.strip():
                        user_data["user_name"] = user_name

                    # 更新其他字段
                    user_data.update({
                        "lifestyle": lifestyle,
                        "interests": interests,
                    })

                    # 如果设置了AI角色，保存到用户数据中
                    if ai_role:
                        user_data["role"] = ai_role
                        self.logger.bind(tag=TAG).info(f"[用户档案接口] 根据AI性别'{ai_gender}'设置角色为: {ai_role}")

                    # 如果设置了AI名字，保存为assistant_name
                    if ai_name:
                        user_data["assistant_name"] = ai_name
                        self.logger.bind(tag=TAG).info(f"[用户档案接口] 设置AI助手名字为: {ai_name}")

                    # 保存用户数据
                    self.redis_client.save_user_data(client_id, user_data)

                    # 记录存储到Redis的原始JSON数据
                    self.logger.bind(tag=TAG).info(f"[用户档案接口] 往用户记忆存储的数据 - client_id: {client_id}, 原始JSON: {json.dumps(user_data, ensure_ascii=False)}")
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"[用户档案接口] 保存用户数据到Redis失败: {e}")

            # 生成个性化问候语（处理user_name为空的情况）
            greeting = await self._generate_personalized_greeting(user_name, lifestyle, interests, ai_gender, ai_name)

            # 获取设置的角色信息
            ai_role = self._determine_ai_role(ai_gender)

            response_data = {
                "success": True,
                "message": "用户档案设置成功",
                "data": {
                    "user_name": user_name if user_name.strip() else None,  # 容错处理：空字符串返回null
                    "lifestyle": lifestyle,
                    "interests": interests,
                    "ai_gender": ai_gender,
                    "ai_name": ai_name,
                    "ai_role": ai_role,
                    "personalized_greeting": greeting
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[用户档案接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except json.JSONDecodeError:
            response = web.json_response({
                "success": False,
                "message": "JSON格式错误"
            }, status=400)
            return self._add_cors_headers(response)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"设置用户档案时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            }, status=500)
            return self._add_cors_headers(response)

    async def _handle_text_summary(self, request: web_request.Request) -> Response:
        """文本摘要生成会议纪要"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
                
            # 解析请求数据
            data = await request.json()

            # 记录客户端请求数据
            self.logger.bind(tag=TAG).info(f"[文本摘要接口] 收到客户端请求: {json.dumps(data, ensure_ascii=False)}")

            # 验证必要字段
            if not data or not isinstance(data, dict):
                response = web.json_response({
                    "success": False,
                    "message": "请求数据格式错误，需要JSON格式"
                }, status=400)
                return self._add_cors_headers(response)

            text_content = data.get("text", "")
            if not text_content or not text_content.strip():
                response = web.json_response({
                    "success": False,
                    "message": "文本内容不能为空"
                }, status=400)
                return self._add_cors_headers(response)

            # 生成会议纪要
            summary = await self._generate_meeting_summary(text_content.strip())

            response_data = {
                "success": True,
                "message": "会议纪要生成成功",
                "data": {
                    "original_text_length": len(text_content),
                    "summary": summary
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[文本摘要接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except json.JSONDecodeError:
            response = web.json_response({
                "success": False,
                "message": "JSON格式错误"
            }, status=400)
            return self._add_cors_headers(response)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成文本摘要时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            }, status=500)
            return self._add_cors_headers(response)

    async def _generate_personalized_greeting(self, user_name: str, lifestyle: str, interests: str, ai_gender: str = "", ai_name: str = "") -> str:
        """生成个性化问候语"""
        try:
            if not self.llm:
                if user_name and user_name.strip():
                    return f"嗨，{user_name}！很高兴认识你！"
                else:
                    return "嗨！很高兴认识你！"

            # 构建系统提示词，考虑AI性别和名字
            gender_context = self._get_gender_context(ai_gender)

            system_prompt = f"""你是一个友善的AI助手{gender_context}，需要根据用户的信息生成个性化的问候语。

要求：
1. 语气要亲切友好，充满好奇心
2. 要体现对用户生活方式和兴趣爱好的关注
3. 要表达期待进一步了解用户的愿望
4. 语言要自然流畅，像朋友间的对话
5. 长度控制在100字以内
6. 如果用户给AI起了名字，要在问候语中自然地提及这个名字
7. 要体现出AI的性别特征（如果用户指定了性别）

请根据用户信息生成一段个性化问候语，注意符合自己的性别：
1、引用用户的生活方式和兴趣爱好，表达对用户的关注
2、邀请用户进一步交流"""

            # 构建用户提示词
            user_name_display = user_name if user_name and user_name.strip() else '未提供'
            user_prompt = f"""用户信息：
- 昵称：{user_name_display}
- 生活方式：{lifestyle if lifestyle else '未提供'}
- 兴趣爱好：{interests if interests else '未提供'}
- 希望AI的性别：{ai_gender if ai_gender else '未指定'}
- 给AI起的名字：{ai_name if ai_name else '未指定'}

请生成个性化问候语："""

            # 记录传给LLM的数据
            self.logger.bind(tag=TAG).info(f"[个性化问候语] 传给LLM的系统提示词: {system_prompt}")
            self.logger.bind(tag=TAG).info(f"[个性化问候语] 传给LLM的用户提示词: {user_prompt}")

            # 调用LLM生成问候语
            greeting = self.llm.response_no_stream(system_prompt, user_prompt)

            # 记录LLM返回的数据
            self.logger.bind(tag=TAG).info(f"[个性化问候语] LLM返回的原始结果: {greeting}")

            if greeting and greeting.strip():
                final_greeting = greeting.strip()
                self.logger.bind(tag=TAG).info(f"[个性化问候语] 为用户 {user_name} 生成个性化问候语成功，最终结果: {final_greeting}")
                return final_greeting
            else:
                if user_name and user_name.strip():
                    fallback_greeting = f"嗨，{user_name}！很高兴认识你！期待与你的交流！"
                else:
                    fallback_greeting = "嗨！很高兴认识你！期待与你的交流！"
                self.logger.bind(tag=TAG).warning(f"[个性化问候语] LLM返回空结果，使用默认问候语: {fallback_greeting}")
                return fallback_greeting

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成个性化问候语失败: {e}")
            if user_name and user_name.strip():
                return f"嗨，{user_name}！很高兴认识你！"
            else:
                return "嗨！很高兴认识你！"

    async def _generate_meeting_summary(self, text_content: str) -> str:
        """生成会议纪要"""
        try:
            if not self.llm:
                return "抱歉，LLM服务不可用，无法生成会议纪要。"

            # 获取服务器当前时间
            import datetime
            current_time = datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M")

            # 构建系统提示词
            system_prompt = f"""你是一个专业的会议纪要整理助手，需要将用户提供的文本内容整理成规范的会议纪要。

要求：
1. 提取关键信息，包括：会议主题、主要讨论内容、决议事项、行动计划等
2. 按照标准会议纪要格式组织内容
3. 语言要简洁明了，条理清晰
4. 突出重点决议和后续行动
5. 如果原文信息不完整，请根据现有信息尽力整理

会议纪要格式参考：
# 会议纪要

## 基本信息
- 会议时间：{current_time}
- 会议主题：[从文本中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要。"""

            # 构建用户提示词
            user_prompt = f"请将以下文本内容整理成会议纪要：\n\n{text_content}"

            # 记录传给LLM的数据
            self.logger.bind(tag=TAG).info(f"[会议纪要] 传给LLM的系统提示词: {system_prompt}")
            self.logger.bind(tag=TAG).info(f"[会议纪要] 传给LLM的用户提示词: {user_prompt}")

            # 调用LLM生成会议纪要
            summary = self.llm.response_no_stream(system_prompt, user_prompt)

            # 记录LLM返回的数据
            self.logger.bind(tag=TAG).info(f"[会议纪要] LLM返回的原始结果: {summary}")

            if summary and summary.strip():
                final_summary = summary.strip()
                self.logger.bind(tag=TAG).info(f"[会议纪要] 生成成功，最终结果: {final_summary}")
                return final_summary
            else:
                fallback_summary = "抱歉，无法生成会议纪要，请检查输入内容。"
                self.logger.bind(tag=TAG).warning(f"[会议纪要] LLM返回空结果，使用默认回复: {fallback_summary}")
                return fallback_summary

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成会议纪要失败: {e}")
            return f"生成会议纪要时发生错误：{str(e)}"

    def _get_gender_context(self, ai_gender: str) -> str:
        """获取性别上下文"""
        if ai_gender:
            if ai_gender.lower() in ['女', 'female', '女性']:
                return "（女性）"
            elif ai_gender.lower() in ['男', 'male', '男性']:
                return "（男性）"
        return ""

    def _determine_ai_role(self, ai_gender: str) -> str:
        """根据AI性别确定对应的角色

        Args:
            ai_gender: 用户选择的AI性别

        Returns:
            str: 对应的角色名称，如果性别无效则返回空字符串
        """
        if not ai_gender:
            return ""

        if ai_gender.lower() in ['男', 'male', '男性']:
            return "治愈男闺蜜"
        elif ai_gender.lower() in ['女', 'female', '女性']:
            return "俏皮女闺蜜"
        else:
            self.logger.bind(tag=TAG).warning(f"未识别的AI性别: {ai_gender}")
            return ""