import time
import copy
from core.utils.util import remove_punctuation_and_length
from core.handle.sendAudioHandle import send_meeting_message, send_stt_message
from core.handle.intentHandler import handle_user_intent
from core.utils.output_counter import check_device_output_limit
from core.handle.reportHandle import enqueue_asr_report
from core.utils.util import audio_to_data

TAG = __name__


async def handleAudioMessage(conn, audio):
    if conn.vad is None:
        return
    if not conn.asr_server_receive:
        # conn.logger.bind(tag=TAG).debug(f"前期数据处理中，暂停接收")
        return
    if conn.client_listen_mode == "auto" or conn.client_listen_mode == "realtime":
        have_voice = conn.vad.is_vad(conn, audio)
    else:
        have_voice = conn.client_have_voice

    # 如果本次没有声音，本段也没声音，就把声音丢弃了
    if have_voice == False and conn.client_have_voice == False:
        conn.asr_audio.append(audio)
        conn.asr_audio = conn.asr_audio[
            -10:
        ]  # 保留最新的10帧音频内容，解决ASR句首丢字问题
        return
    conn.asr_audio.append(audio)
    # 如果本段有声音，且已经停止了
    if conn.client_voice_stop:
        conn.client_abort = False
        conn.asr_server_receive = False
        # 音频太短了，无法识别
        if len(conn.asr_audio) < 15:
            conn.asr_server_receive = True
        else:
            text, _ = await conn.asr.speech_to_text(conn.asr_audio, conn.session_id)
            text_len, _ = remove_punctuation_and_length(text)
            if text_len > 0:
                # 使用自定义模块进行上报
                enqueue_asr_report(conn, text, copy.deepcopy(conn.asr_audio))

                await startToChat(conn, text)
            else:
                conn.asr_server_receive = True
        conn.asr_audio.clear()
        conn.reset_vad_states()


async def startToChat(conn, text):
    # 用户开始说话时，立即中断并清空当前的TTS队列
    # 因为用户不会再听之前的AI回复了
    if hasattr(conn, 'tts_queue') and hasattr(conn, 'audio_play_queue'):
        # 检查队列中是否有未完成的TTS任务
        tts_queue_size = conn.tts_queue.qsize()
        audio_queue_size = conn.audio_play_queue.qsize()
        
        if tts_queue_size > 0 or audio_queue_size > 0:
            conn.logger.bind(tag=TAG).info(f"🔇 用户开始说话，中断TTS队列 - TTS队列: {tts_queue_size}个, 音频队列: {audio_queue_size}个")
            
            # 设置中断标志
            conn.client_abort = True
            
            # 清空TTS和音频播放队列（不终止线程，只清空队列）
            conn.clear_queues(send_poison_pill=False)
            
            # 发送TTS停止消息给客户端
            try:
                import json
                await conn.websocket.send(
                    json.dumps({"type": "tts", "state": "stop", "session_id": conn.session_id})
                )
                conn.logger.bind(tag=TAG).debug("📤 已发送TTS停止消息给客户端")
            except Exception as e:
                conn.logger.bind(tag=TAG).warning(f"发送TTS停止消息失败: {e}")
            
            # 清除语音状态
            conn.clearSpeakStatus()
    
    # 会议记录模式：只进行ASR识别，跳过后续所有处理
    if conn.meeting_mode:
        await send_meeting_message(conn, text)
        conn.asr_server_receive = True
        conn.logger.bind(tag=TAG).info(f"会议记录模式 - ASR结果: {text}")
        return

    if conn.need_bind:
        await check_bind_device(conn)
        return

    # 如果当日的输出字数大于限定的字数
    if conn.max_output_size > 0:
        if check_device_output_limit(
            conn.headers.get("device-id"), conn.max_output_size
        ):
            await max_out_size(conn)
            return

    # 首先进行意图分析
    intent_handled = await handle_user_intent(conn, text)

    if intent_handled:
        # 如果意图已被处理，不再进行聊天
        conn.asr_server_receive = True
        return

    # 意图未被处理，继续常规聊天流程
    # 【重要修复】重置中断标志，允许新的TTS任务处理
    conn.client_abort = False
    conn.logger.bind(tag=TAG).debug("🔄 重置client_abort标志，准备开始新的聊天")

    await send_stt_message(conn, text)
    
    # 提交聊天任务到线程池，并添加异常处理
    try:
        if conn.intent_type == "function_call":
            # 使用支持function calling的聊天方法
            future = conn.executor.submit(conn.chat_with_function_calling, text)
        else:
            future = conn.executor.submit(conn.chat, text)
        
        # 为future添加完成回调，用于捕获线程池中的异常
        def handle_chat_exception(fut):
            try:
                fut.result()  # 获取结果，如果有异常会抛出
            except Exception as e:
                conn.logger.bind(tag=TAG).error(f"🚨 聊天处理线程异常: {e}")
                import traceback
                conn.logger.bind(tag=TAG).debug(f"🚨 聊天线程异常堆栈: {traceback.format_exc()}")
        
        future.add_done_callback(handle_chat_exception)
        
    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"🚨 提交聊天任务到线程池失败: {e}")
        # 设置ASR接收状态，让系统可以处理下一个请求
        conn.asr_server_receive = True




async def max_out_size(conn):
    text = "不好意思，我现在有点事情要忙，明天这个时候我们再聊，约好了哦！明天不见不散，拜拜！"
    await send_stt_message(conn, text)
    conn.tts_first_text_index = 0
    conn.tts_last_text_index = 0
    conn.llm_finish_task = True
    file_path = "config/assets/max_output_size.wav"
    opus_packets, _ = audio_to_data(file_path)
    conn.audio_play_queue.put((opus_packets, text, 0))
    conn.close_after_chat = True


async def check_bind_device(conn):
    if conn.bind_code:
        # 确保bind_code是6位数字
        if len(conn.bind_code) != 6:
            conn.logger.bind(tag=TAG).error(f"无效的绑定码格式: {conn.bind_code}")
            text = "绑定码格式错误，请检查配置。"
            await send_stt_message(conn, text)
            return

        text = f"请登录控制面板，输入{conn.bind_code}，绑定设备。"
        await send_stt_message(conn, text)
        conn.tts_first_text_index = 0
        conn.tts_last_text_index = 6
        conn.llm_finish_task = True

        # 播放提示音
        music_path = "config/assets/bind_code.wav"
        opus_packets, _ = audio_to_data(music_path)
        conn.audio_play_queue.put((opus_packets, text, 0))

        # 逐个播放数字
        for i in range(6):  # 确保只播放6位数字
            try:
                digit = conn.bind_code[i]
                num_path = f"config/assets/bind_code/{digit}.wav"
                num_packets, _ = audio_to_data(num_path)
                conn.audio_play_queue.put((num_packets, None, i + 1))
            except Exception as e:
                conn.logger.bind(tag=TAG).error(f"播放数字音频失败: {e}")
                continue
    else:
        text = f"没有找到该设备的版本信息，请正确配置 OTA地址，然后重新编译固件。"
        await send_stt_message(conn, text)
        conn.tts_first_text_index = 0
        conn.tts_last_text_index = 0
        conn.llm_finish_task = True
        music_path = "config/assets/bind_not_found.wav"
        opus_packets, _ = audio_to_data(music_path)
        conn.audio_play_queue.put((opus_packets, text, 0))
