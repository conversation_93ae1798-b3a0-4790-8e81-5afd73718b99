import time
import datetime
from typing import Dict, Any, Optional
from config.logger import setup_logging

class UserProfileManager:
    """
    Manages user-related and chat-related functionality including:
    - User profile management
    - Role configuration
    - Chat scenarios and greetings
    - User settings persistence
    """
    
    def __init__(self, connection):
        """
        Initialize the UserProfileManager with a connection to the parent ConnectionHandler.
        
        Args:
            connection: Reference to the parent ConnectionHandler instance
        """
        self.connection = connection
        self.logger = setup_logging()
        
        # Role and assistant settings
        self.current_role = None
        self.assistant_name = "语嫣"  # Default assistant name
        self.user_name = None  # User's name/nickname
        
        # Proactive chat settings
        self.last_proactive_time = {}  # Track last proactive message time per user
        self.daily_greetings_sent = {}  # Track daily greetings sent per user per scenario
        self.proactive_cooldown = 3600 * 3  # 3 hour cooldown between proactive messages
        self.pending_proactive_reply = {}  # Track users who received proactive messages but haven't replied
        
        
    async def load_user_profile(self):
        """Load user's role, assistant name, voice settings, and other profile information"""
        try:
            # Load user settings from Redis or memory system
            user_data = None
            if self.connection.redis_client and self.connection.redis_client.enabled and self.connection.client_id:
                try:
                    user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                    if user_data:
                        self.logger.info(f"[用户配置] 从Redis加载用户数据成功: client_id={self.connection.client_id}, 数据={user_data}")
                    else:
                        self.logger.info(f"[用户配置] Redis中未找到用户数据: client_id={self.connection.client_id}")
                except Exception as e:
                    self.logger.error(f"[用户配置] 从Redis加载用户数据失败: {e}")
            else:
                self.logger.warning("[用户配置] Redis客户端不可用，使用默认设置")
            # Set role from user data or use default
            if user_data and "role" in user_data:
                self.current_role = user_data["role"]
                self.logger.info(f"[用户配置] 从用户数据加载角色: {self.current_role}")
            else:
                self.current_role = self.connection.role_loader.get_default_role()
                self.logger.info(f"[用户配置] 使用默认角色: {self.current_role}")
                
            # Set assistant name from user data if available
            if user_data and "assistant_name" in user_data:
                self.assistant_name = user_data["assistant_name"]
                self.logger.info(f"Loaded assistant name: {self.assistant_name}")
                
            # Set user name from user data if available
            if user_data and "user_name" in user_data:
                self.user_name = user_data["user_name"]
                self.logger.info(f"Loaded user name: {self.user_name}")
                
            # Apply role configuration based on user data
            await self.apply_role_config(user_data)
            
            # TTS voice will be set by apply_role_config based on the loaded role
            # No need to separately load voice here as it's tied to the role
                    
        except Exception as e:
            self.logger.error(f"Failed to load user profile: {e}")
            # Fall back to default settings
            self.current_role = self.connection.role_loader.get_default_role()
            await self.apply_role_config()
    
    async def apply_role_config(self, user_data: Optional[Dict[str, Any]] = None):
        """
        Apply role configuration and handle different chat scenarios (first meeting, etc.)
        
        Args:
            user_data: Optional user data dictionary containing profile information
        """
        try:
            role_config = self.connection.role_loader.get_role_config(self.current_role)
            settings_changed = False
            
            if not role_config:
                self.logger.warning(f"Role config not found: {self.current_role} 如果没有找到，那么用 data/.roles.yaml 里配置的默认角色")
                # 使用默认角色作为兜底
                default_role = self.connection.role_loader.get_default_role()
                self.current_role = default_role
                role_config = self.connection.role_loader.get_role_config(default_role)
                if not role_config:
                    self.logger.error(f"Default role config also not found: {default_role}")
                    return
                self.logger.info(f"Using default role as fallback: {default_role}")
                settings_changed = True  # 角色发生了变化，需要保存
                
            # Replace assistant name placeholder
            prompt = role_config["prompt"].replace("{{assistant_name}}", self.assistant_name)
            
            # Time guidance will be added later in the returning user section if needed
            
            # Scenario detection: Check if this is the first meeting
            is_first_meeting = await self._check_first_meeting()
            
            # Check proactive conditions for all scenarios
            should_proactive = False
            if is_first_meeting:
                # First meetings should always be proactive and enthusiastic
                should_proactive = True
                scenario = self.get_current_time_scenario()
                if scenario:
                    # Mark as pending proactive reply for first meeting
                    self.pending_proactive_reply[self.connection.client_id] = {
                        'time': time.time(),
                        'scenario': f"first_meeting_{scenario}"
                    }
                    self.logger.info(f"First meeting proactive greeting for {scenario}, waiting for user reply to start cooldown")
            else:
                should_proactive = self.should_send_proactive_message()
                if should_proactive:
                    # Mark that we've sent a proactive message but haven't started cooldown yet
                    scenario = self.get_current_time_scenario()
                    if scenario:
                        today = datetime.datetime.now().strftime("%Y-%m-%d")
                        client_daily_key = f"{self.connection.client_id}_{today}"
                        
                        if client_daily_key not in self.daily_greetings_sent:
                            self.daily_greetings_sent[client_daily_key] = set()
                        self.daily_greetings_sent[client_daily_key].add(scenario)
                        
                        # Mark as pending proactive reply instead of starting cooldown immediately
                        self.pending_proactive_reply[self.connection.client_id] = {
                            'time': time.time(),
                            'scenario': scenario
                        }
                        
                        self.logger.info(f"Proactive greeting sent for {scenario}, waiting for user reply to start cooldown")
            
            # Add time-aware guidance for all scenarios
            time_guidance = self._generate_time_guidance(should_proactive, is_first_meeting, role_config)
            if time_guidance:
                prompt += f"\n\n{time_guidance}"
                self.logger.info(f"Added time-aware guidance: {time_guidance}")
            
            # Add returning user context only for non-first meetings
            if not is_first_meeting and user_data and user_data.get("last_access_time"):
                # Generate greeting context for returning user
                greeting_context = self._generate_returning_greeting(user_data["last_access_time"])
                prompt += f"\n{greeting_context}"
                self.logger.info(f"Added returning user greeting context: {greeting_context[:100]}...")
            
            # Update connection config with the new prompt
            self.connection.config["prompt"] = prompt
            self.logger.info(f"Applied role config: {self.current_role}, Assistant: {self.assistant_name}")
            
            # Update TTS voice if specified in role config
            voice = role_config.get("voice")
            if voice and hasattr(self.connection, "tts") and self.connection.tts is not None and hasattr(self.connection.tts, 'voice'):
                old_voice = getattr(self.connection.tts, 'voice', None)
                self.connection.tts.voice = voice
                self.logger.info(f"[声音配置] 更新TTS声音配置: client_id={self.connection.client_id}, 角色={self.current_role}, 从'{old_voice}'更改为'{voice}'")
                self.logger.info(f"[声音配置] TTS实例类型: {type(self.connection.tts).__name__}")
                
            # Only save user settings if there were changes
            if settings_changed:
                self.save_user_settings()
                self.logger.debug("User settings saved due to configuration changes")
                
        except Exception as e:
            self.logger.error(f"Failed to apply role config: {e}")
    
    async def _check_first_meeting(self) -> bool:
        """Check if this is the user's first meeting with the assistant"""
        if not (self.connection.redis_client and self.connection.redis_client.enabled and self.connection.client_id):
            return True
            
        try:
            # Check if memory exists
            memory_exists = self.connection.redis_client.get_memory(self.connection.client_id) is not None
            if not memory_exists:
                self.logger.info(f"First meeting detected: client_id={self.connection.client_id}")
                return True
        except Exception as e:
            self.logger.warning(f"Failed to check first meeting status: {e}")
            
        return False
    
    def _generate_returning_greeting(self, last_access_time: float) -> str:
        """Generate an appropriate greeting based on time since last access
        
        Args:
            last_access_time: Timestamp of last access
            
        Returns:
            str: Generated greeting message
        """
        current_time = time.time()
        time_diff = current_time - float(last_access_time)
        
        # Generate base greeting based on time difference
        if time_diff < 60:  # Less than 1 minute
            return "1分钟内你和用户刚刚聊过。"
        elif time_diff < 3600:  # Less than 1 hour
            minutes = int(time_diff / 60)
            return f"{minutes}分钟前你刚和用户聊过。"
        elif time_diff < 86400:  # Less than 1 day
            hours = int(time_diff / 3600)
            return f"你在{hours}小时前和用户聊过。"
        elif time_diff < 604800:  # Less than 1 week
            days = int(time_diff / 86400)
            return f"你在{days}天前和用户聊过。"
        else:  # More than 1 week
            weeks = int(time_diff / 604800)
            return f"你和用户有{weeks}周没聊过了。"
    
    def save_user_settings(self):
        """Save user settings to Redis"""
        if not self.connection.client_id:
            return
            
        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id) or {}
                user_data["role"] = self.current_role
                user_data["assistant_name"] = self.assistant_name
                if self.user_name:
                    user_data["user_name"] = self.user_name
                
                self.connection.redis_client.save_user_data(self.connection.client_id, user_data)
                
                # Get voice info for logging
                log_msg = f"User settings saved: Role={self.current_role}, Assistant={self.assistant_name}"
                if self.user_name:
                    log_msg += f", User={self.user_name}"
                if self.current_role:
                    role_config = self.connection.role_loader.get_role_config(self.current_role)
                    if role_config and role_config.get("voice"):
                        log_msg += f", Voice={role_config['voice']}"
                self.logger.info(log_msg)
        except Exception as e:
            self.logger.error(f"Failed to save user settings: {e}")
    
    def set_user_name(self, user_name: str) -> bool:
        """
        Set user name and save to Redis
        
        Args:
            user_name: User's name or nickname
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not user_name or not user_name.strip():
                self.logger.warning("User name is empty or invalid")
                return False
                
            old_name = self.user_name
            self.user_name = user_name.strip()
            
            # Save to Redis immediately
            self.save_user_settings()
            
            # Log the change
            if old_name:
                self.logger.info(f"User name updated: {old_name} -> {self.user_name}")
            else:
                self.logger.info(f"User name set: {self.user_name}")
                
            return True
        except Exception as e:
            self.logger.error(f"Failed to set user name: {e}")
            return False
    
    def get_user_name(self) -> Optional[str]:
        """
        Get current user name

        Returns:
            str: User name if set, None otherwise
        """
        return self.user_name

    def set_assistant_name(self, assistant_name: str) -> bool:
        """
        Set assistant name and save to Redis

        Args:
            assistant_name: Assistant's name

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not assistant_name or not assistant_name.strip():
                self.logger.warning("Assistant name is empty or invalid")
                return False

            old_name = self.assistant_name
            self.assistant_name = assistant_name.strip()

            # Save to Redis immediately
            self.save_user_settings()

            # Log the change
            if old_name != self.assistant_name:
                self.logger.info(f"Assistant name updated: {old_name} -> {self.assistant_name}")
            else:
                self.logger.info(f"Assistant name set: {self.assistant_name}")

            return True
        except Exception as e:
            self.logger.error(f"Failed to set assistant name: {e}")
            return False

    def get_assistant_name(self) -> str:
        """
        Get current assistant name

        Returns:
            str: Assistant name
        """
        return self.assistant_name

    def get_current_time_scenario(self) -> str:
        """Determine current time scenario (morning/noon/afternoon/evening)"""
        current_hour = datetime.datetime.now().hour
        current_minute = datetime.datetime.now().minute

        if 6 <= current_hour < 11:
            return "morning"
        elif 11 <= current_hour < 14:
            return "noon"
        elif 14 <= current_hour < 17 or (current_hour == 17 and current_minute < 30):
            return "afternoon"
        else:
            return "evening"  # 17:30-6:00 is evening

    def _generate_time_guidance(self, should_proactive: bool = None, is_first_meeting: bool = False, role_config: dict = None) -> str:
        """Generate time-aware guidance for system prompt to handle natural greetings

        Args:
            should_proactive: Whether to include proactive guidance (pass from caller to avoid duplicate checks)
            is_first_meeting: Whether this is the user's first meeting with the assistant
            role_config: Role configuration dictionary containing initial_greeting if applicable
        """
        scenario = self.get_current_time_scenario()
        if not scenario:
            return ""

        # Use passed parameter or check (for backward compatibility)
        if should_proactive is None:
            should_proactive = self.should_send_proactive_message()

        if should_proactive:
            guidance_templates = {
                "morning": '''## 早晨时段指导 (6:00-11:00)
- 当用户say Hi时，要表现得特别热情主动！要向用户问好，还要主动关心和延伸话题
- 用活泼温暖的语气开场，主动询问和关心，例如早餐吃了吗、今天有什么计划、精神状态如何等
- 用充满朝气的语调，多用"好"、"不错"、"棒"等积极词汇，营造愉快的开始''',

                "noon": '''## 午间时段指导 (11:00-14:00)
- 当用户say Hi时，要表现得特别贴心主动！要向用户问好，还要主动关心和延伸话题
- 用轻松贴心的语气开场，主动询问和关心，例如午餐情况、工作状态、需要休息吗等
- 用温和贴心的语调，让用户感受到被关怀''',

                "afternoon": '''## 下午时段指导 (14:00-17:30)
- 当用户say Hi时，要表现得特别亲切主动！要向用户问好，还要主动关心和延伸话题
- 用轻快亲切的语气开场，主动询问和关心，例如工作状态、下午安排、需要放松一下吗等
- 用活泼亲切的语调，给人放松愉快的感觉''',

                "evening": '''## 晚间时段指导 (17:30-6:00)
- 当用户say Hi时，要表现得特别温馨主动！要向用户问好，还要主动关心和延伸话题
- 用温馨舒缓的语气开场，主动询问和关心，例如一天的感受、放松方式、明日打算、心情如何等，如果夜深了提醒注意休息
- 用轻柔舒缓的语调，给人温暖陪伴感'''
            }
        else:
            guidance_templates = {
                "morning": '''## 早晨时段指导 (6:00-11:00)
- 当用户say Hi时，礼貌友好地回应，但相对温和不过于主动
- 用自然的语气开场，可以简单询问或关心，但不要连续提问，等用户主导话题方向
- 保持朋友般的亲近感，语调活泼但不过分热情''',

                "noon": '''## 午间时段指导 (11:00-14:00)
- 当用户say Hi时，礼貌友好地回应，但相对温和不过于主动
- 用自然的语气开场，可以简单询问或关心，但不要连续提问，等用户主导话题方向
- 保持朋友般的关怀，语调温和贴心''',

                "afternoon": '''## 下午时段指导 (14:00-17:30)
- 当用户say Hi时，礼貌友好地回应，但相对温和不过于主动
- 用自然的语气开场，可以简单询问或关心，但不要连续提问，等用户主导话题方向
- 保持轻松自然的对话氛围''',

                "evening": '''## 晚间时段指导 (17:30-6:00)
- 当用户say Hi时，礼貌友好地回应，但相对温和不过于主动
- 用自然的语气开场，可以简单询问或关心，但不要连续提问，等用户主导话题方向
- 保持温馨的朋友感，语调轻柔舒缓'''
            }

        base_guidance = "当前时间: " + time.strftime("%Y年%m月%d日 %H:%M") + "\n" + guidance_templates.get(scenario, "")

        # Add initial greeting for first meetings
        if is_first_meeting and role_config and "initial_greeting" in role_config:
            initial_greeting = role_config["initial_greeting"].replace("{{assistant_name}}", self.assistant_name)
            base_guidance += f"\n\n## 首次见面特别指导\n{initial_greeting}"
            self.logger.info(f"Added initial greeting to time guidance for first meeting")

        return base_guidance

    def should_send_proactive_message(self) -> bool:
        """Check if we should send a proactive message based on cooldown, scenario, and daily limits"""
        if not self.connection.client_id:
            self.logger.info("无client_id，不发送主动消息")
            return False

        # Check if it's an appropriate time scenario
        scenario = self.get_current_time_scenario()
        if not scenario:
            current_hour = datetime.datetime.now().hour
            self.logger.info(f"当前时间({current_hour}点)不适合发送主动消息")
            return False

        # Check if we've already sent a greeting for this scenario today
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        client_daily_key = f"{self.connection.client_id}_{today}"

        if client_daily_key not in self.daily_greetings_sent:
            self.daily_greetings_sent[client_daily_key] = set()

        if scenario in self.daily_greetings_sent[client_daily_key]:
            self.logger.info(f"今日已发送过{scenario}问候，跳过")
            return False  # Already sent greeting for this time period today

        # Check cooldown (load from Redis if not in memory)
        current_time = time.time()
        last_time = self._get_last_proactive_time()
        time_diff = current_time - last_time

        if time_diff < self.proactive_cooldown:
            remaining = self.proactive_cooldown - time_diff
            self.logger.info(f"主动消息冷却中，还需{remaining:.0f}秒")
            return False

        # Check if user has been inactive (important: don't interrupt active conversations)
        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                if user_data and user_data.get("last_access_time"):
                    # Only send proactive message if user has been away for at least 30 minutes
                    time_since_last_access = current_time - float(user_data["last_access_time"])
                    # 检查是否是新连接（连接时间少于5秒）
                    connection_duration = current_time - getattr(self.connection, 'connection_start_time', current_time)
                    is_new_connection = connection_duration < 5

                    # 新连接检测：只有当上次访问时间超过冷却期时才允许新连接打招呼
                    if is_new_connection:
                        if time_diff >= self.proactive_cooldown:
                            self.logger.info(f"新连接检测到，且已过冷却期，允许主动打招呼")
                        else:
                            remaining = self.proactive_cooldown - time_diff
                            self.logger.info(f"新连接检测到，但冷却期未过，还需{remaining:.0f}秒")
                            return False
                    elif time_since_last_access < 1800:  # 30 minutes
                        self.logger.info(f"用户最近活动时间太近({time_since_last_access:.0f}秒前)，不发送主动消息")
                        return False
                else:
                    self.logger.info("用户数据中无last_access_time，允许发送主动消息")
            else:
                self.logger.info("Redis不可用，允许发送主动消息")
        except Exception as e:
            self.logger.warning(f"Failed to check last access time: {e}")

        self.logger.info(f"所有条件满足，可以发送{scenario}主动消息")
        return True

    def _get_last_proactive_time(self) -> float:
        """Get last proactive time from memory or Redis"""
        if not self.connection.client_id:
            return 0

        # Check memory first
        if self.connection.client_id in self.last_proactive_time:
            return self.last_proactive_time[self.connection.client_id]

        # Load from Redis if not in memory
        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                if user_data:
                    # Load last proactive time
                    if "last_proactive_time" in user_data:
                        last_time = float(user_data["last_proactive_time"])
                        self.last_proactive_time[self.connection.client_id] = last_time
                        return last_time
        except Exception as e:
            self.logger.warning(f"Failed to load last proactive time from Redis: {e}")

        return 0

    def handle_user_reply(self):
        """Handle user reply to proactive message - start cooldown timer"""
        if not self.connection.client_id:
            return

        # Check if there's a pending proactive reply
        if self.connection.client_id in self.pending_proactive_reply:
            pending_info = self.pending_proactive_reply[self.connection.client_id]

            # For first meetings, we need special handling to distinguish between:
            # 1. User's initial message that triggers the bot's proactive greeting
            # 2. User's reply to the bot's proactive greeting
            if pending_info['scenario'].startswith('first_meeting_'):
                # Use an instance variable to track if bot has sent proactive greeting in this session
                if not hasattr(self, '_bot_has_sent_first_greeting'):
                    # This is the user's first message that triggers bot's proactive greeting
                    self._bot_has_sent_first_greeting = True
                    self.logger.info(f"User's first message triggered bot's proactive greeting, not starting cooldown yet")
                    return
                else:
                    # This is a subsequent message - user is replying to bot's proactive greeting
                    self.pending_proactive_reply.pop(self.connection.client_id)
                    # Now start the cooldown timer
                    self.last_proactive_time[self.connection.client_id] = time.time()
                    self._save_proactive_time_to_redis()
                    self.logger.info(f"User replied to proactive {pending_info['scenario']} greeting, cooldown started")
            else:
                # For non-first meetings, start cooldown immediately
                self.pending_proactive_reply.pop(self.connection.client_id)
                # Now start the cooldown timer
                self.last_proactive_time[self.connection.client_id] = time.time()
                self._save_proactive_time_to_redis()
                self.logger.info(f"User replied to proactive {pending_info['scenario']} greeting, cooldown started")

    def _save_proactive_time_to_redis(self):
        """Save last proactive time to Redis"""
        if not self.connection.client_id:
            return

        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id) or {}
                user_data["last_proactive_time"] = self.last_proactive_time.get(self.connection.client_id, 0)
                self.connection.redis_client.save_user_data(self.connection.client_id, user_data)
                self.logger.debug(f"Saved last proactive time to Redis: {user_data['last_proactive_time']}")
        except Exception as e:
            self.logger.error(f"Failed to save proactive time to Redis: {e}")


    def _cleanup_old_daily_records(self):
        """Clean up old daily greeting records to prevent memory leaks"""
        try:
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            keys_to_remove = []

            for key in self.daily_greetings_sent.keys():
                # Extract date from key (format: client_id_YYYY-MM-DD)
                if "_" in key:
                    key_date = key.split("_")[-1]
                    if key_date != current_date:
                        keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.daily_greetings_sent[key]

            if keys_to_remove:
                self.logger.info(f"Cleaned up {len(keys_to_remove)} old daily greeting records")

        except Exception as e:
            self.logger.warning(f"Failed to cleanup old daily records: {e}")
