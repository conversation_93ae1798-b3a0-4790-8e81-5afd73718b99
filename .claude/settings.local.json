{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"change_role|ICL_zh_male_nuanxintitie_tob|已更新TTS实例的声音为\" /Users/<USER>/Projects/yuyan-server/tmp/server.log -A 5 -B 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def _save_user_settings|def save_user_settings\" /Users/<USER>/Projects/yuyan-server/core/connection.py -A 20)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def save_user_settings\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 25)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"Load TTS voice|TTS voice setting\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 5 -B 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def apply_role_config\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 40)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"Apply role configuration based on user data\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 3 -B 3)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"Apply role configuration based on user data\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 15 -B 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def _apply_role_config\" /Users/<USER>/Projects/yuyan-server/core/connection.py -A 30)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"_apply_role_config\" /Users/<USER>/Projects/yuyan-server/plugins_func/functions/change_role.py -A 3 -B 3)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"user_data\\[\\\"role\\\"\\]\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -B 5 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg 'user_data\\[\"role\"\\]' /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -B 5 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"conn.current_role|conn.assistant_name\" /Users/<USER>/Projects/yuyan-server/plugins_func/functions/change_role.py -B 3 -A 3)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"self.current_role|self.assistant_name\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"@property\" /Users/<USER>/Projects/yuyan-server/core/connection.py -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"current_role\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 3 -A 3)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"保存当前角色设置\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"保存当前角色设置\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 15 -A 15)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"TTS voice will be set by apply_role_config\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 2 -B 2)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg 'user_data\\[\"voice\"\\]|user_data\\[\\\"voice\\\"\\]' /Users/<USER>/Projects/yuyan-server/core/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg '\"voice\"' /Users/<USER>/Projects/yuyan-server/core/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg 'user_data.*voice|voice.*user_data' /Users/<USER>/Projects/yuyan-server/core/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg 'user_data' /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"description.*manage_assistant\" /Users/<USER>/Projects/yuyan-server/plugins_func/functions/change_role.py -A 5 -B 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"成熟女闺蜜\" /Users/<USER>/Projects/yuyan-server/roles.yaml)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def get_available_roles\" /Users/<USER>/Projects/yuyan-server/config/role_loader.py -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"yield\" /Users/<USER>/Projects/yuyan-server/core/providers/llm/gemini/gemini.py -A 5 -B 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def _generate\" /Users/<USER>/Projects/yuyan-server/core/providers/llm/gemini/gemini.py -A 100)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"chat_with_function_calling|self.llm.response_with_functions\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 15)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"for.*in.*llm_responses\" /Users/<USER>/Projects/yuyan-server/core/connection.py -A 50)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"client_abort.*=\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"async def close\" /Users/<USER>/Projects/yuyan-server/core/connection.py -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"llm_responses = self.llm.response_with_functions\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"for response in llm_responses:\" /Users/<USER>/Projects/yuyan-server/core/connection.py -A 20)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"yield\" /Users/<USER>/Projects/yuyan-server/core/providers/llm/gemini/gemini.py -n)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"check_and_send_proactive_message|try_send_proactive_message|should_send_proactive_message\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"check_and_send_proactive_message\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 10 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"主动话题|proactive|check_and_send_proactive_message\" /Users/<USER>/Projects/yuyan-server/tmp/server.log)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"主动话题\" /Users/<USER>/Projects/yuyan-server/tmp/server.log)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"_load_user_profile\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"await self._load_user_profile\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 10 -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"开始处理客户端消息\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 15 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def.*response\" /Users/<USER>/Projects/yuyan-server/core/providers/llm/base.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"class Dialogue\" /Users/<USER>/Projects/yuyan-server/core/utils/dialogue.py -A 20)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"客户端断开连接\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"sendAudioHandle\" /Users/<USER>/Projects/yuyan-server/core/user_chat_manager.py -A 10 -B 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"主动话题检查失败\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"async def sendAudioHandle\" /Users/<USER>/Projects/yuyan-server/core/handle/sendAudioHandle.py -A 20)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"def sendAudioHandle\" /Users/<USER>/Projects/yuyan-server/core/handle/sendAudioHandle.py -A 10)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"async def sendAudioHandle|def sendAudioHandle\" /Users/<USER>/Projects/yuyan-server/core/handle/sendAudioHandle.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"sendAudioHandle\" /Users/<USER>/Projects/yuyan-server/core/handle/sendAudioHandle.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"from.*sendAudioHandle.*import\" /Users/<USER>/Projects/yuyan-server/core/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"sendAudioMessage\" /Users/<USER>/Projects/yuyan-server/core/connection.py -B 5 -A 5)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"async def\" /Users/<USER>/Projects/yuyan-server/core/handle/sendAudioHandle.py)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"save_memory|conversation_history|msgs.*=|历史\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"dialogue\\.put|dialogue\\.dialogue\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrip/arm64-darwin/rg -A5 -B5 \"dialogue\\.put.*user\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A5 -B5 \"dialogue\\.put.*user\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "<PERSON><PERSON>(pip show:*)", "Bash(redis-cli get:*)", "Bash(redis-cli keys:*)", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:docs.fish.audio)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 10 -B 2 \"def handle_connection\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 5 -B 2 \"except.*websocket|except.*WebSocket\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 5 -B 2 \"close|disconnect\" /Users/<USER>/Projects/yuyan-server/core/connection.py)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 10 -B 2 \"except\" /Users/<USER>/Projects/yuyan-server/core/connection.py)"], "deny": []}}