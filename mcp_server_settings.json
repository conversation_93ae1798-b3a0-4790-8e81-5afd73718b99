{"des": ["在data目录下创建.mcp_server_settings.json文件，可以选择下面的MCP服务，也可以自行添加新的MCP服务。", "后面不断测试补充好用的mcp服务，欢迎大家一起补充。", "记得删除注释行,des属性仅为说明,不会被解析。", "des和link属性，仅为说明安装方式，方便大家查看原始链接，不是必须项。", "当前支持stdio/sse两种模式。"], "mcpServers": {"playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "des": "run 'npx playwright install' first", "link": "https://github.com/executeautomation/mcp-playwright"}, "bocha-search-mcp": {"command": "uv", "args": ["run", "--directory", "/Users/<USER>/Projects/bocha-search-mcp", "--active", "bocha-search-mcp"], "env": {"BOCHA_API_KEY": "sk-****"}}, "netease_music": {"url": "http://localhost:9382/sse", "type": "sse", "des": "NetEase Cloud Music API with SSE support", "link": "https://github.com/your-username/mcp-netease-music-server"}}}